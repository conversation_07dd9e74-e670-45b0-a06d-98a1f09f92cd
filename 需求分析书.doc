# 新闻APP需求分析书

## 1. 项目概述

### 1.1 项目背景
开发一款聚合型新闻APP，该项目基于flutter 编写 整合多个热门平台的新闻内容，为用户提供个性化的新闻阅读体验。

### 1.2 项目目标
- 提供便捷的邮箱验证码登录方式
- 整合多个新闻源，包括抖音热搜、微博热搜、澎湃新闻等
- 提供个性化的频道订阅和新闻推荐
- 记录用户浏览历史，提升用户体验

## 2. 功能需求

### 2.1 登录模块

#### 2.1.1 邮箱验证码登录
- **功能描述**：用户通过邮箱接收验证码完成登录
- **输入**：邮箱地址
- **处理流程**：
  1. 用户输入邮箱地址
  2. 系统验证邮箱格式有效性
  3. 发送6位数字验证码到用户邮箱
  4. 用户输入验证码
  5. 系统验证码校验（有效期5分钟）
  6. 验证成功后跳转到频道选择页面
- **输出**：登录成功/失败状态
- **异常处理**：
  - 邮箱格式错误提示
  - 验证码过期提示
  - 验证码错误提示（最多尝试5次）

#### 2.1.2 频道选择
- **功能描述**：首次登录用户选择感兴趣的新闻频道
- **可选频道**：
  - 抖音热搜
  - 微博热搜
  - 澎湃新闻
  - （可扩展其他新闻源）
- **选择规则**：至少选择1个频道，最多选择所有频道
- **完成后**：跳转到我的新闻页面

### 2.2 我的新闻模块

#### 2.2.1 频道Tab切换
- **功能描述**：顶部显示用户已选择的频道标签
- **交互方式**：点击Tab切换不同频道的新闻列表
- **显示规则**：
  - 默认显示第一个已选频道
  - Tab可左右滑动（当频道较多时）
  - 当前选中Tab高亮显示

#### 2.2.2 新闻列表
- **功能描述**：展示当前频道的新闻列表
- **列表项信息**：
  - 新闻标题
  - 新闻摘要（限制字数）
  - 发布时间
  - 新闻来源
  - 缩略图（如有）
- **交互功能**：
  - 下拉刷新获取最新新闻
  - 上拉加载更多新闻
  - 点击新闻项跳转到新闻详情页

#### 2.2.3 新闻详情页
- **功能描述**：显示新闻完整内容
- **页面内容**：
  - 新闻标题
  - 发布时间和来源
  - 新闻正文内容
  - 相关图片/视频（如有）
- **功能按钮**：
  - 返回按钮
  - 分享按钮
- **自动记录**：将该新闻添加到浏览记录

### 2.3 个人中心模块

#### 2.3.1 浏览记录
- **功能描述**：显示用户历史浏览的新闻记录
- **记录信息**：
  - 新闻标题
  - 浏览时间
  - 新闻来源
- **排序方式**：按浏览时间倒序排列
- **操作功能**：
  - 点击记录重新打开新闻详情
  - 清空浏览记录功能

#### 2.3.2 频道管理
- **功能描述**：用户可以重新选择或调整关注的频道
- **操作功能**：
  - 添加新频道
  - 取消关注频道
  - 调整频道显示顺序

#### 2.3.3 退出登录
- **功能描述**：用户退出当前账号
- **操作流程**：
  1. 显示确认对话框
  2. 用户确认后清除本地登录信息
  3. 跳转到登录页面

## 3. 非功能需求

### 3.1 性能需求
- 应用启动时间不超过3秒
- 新闻列表加载时间不超过2秒
- 支持离线缓存，提升用户体验

### 3.2 用户体验需求
- 界面简洁美观，符合现代移动应用设计标准
- 操作流畅，响应及时
- 支持深色/浅色主题切换

### 3.3 安全需求
- 验证码传输采用加密方式
- 用户数据本地加密存储
- 防止验证码暴力破解

### 3.4 兼容性需求
- 支持iOS 12.0及以上版本
- 支持Android 7.0及以上版本
- 适配不同屏幕尺寸

## 4. 数据需求

### 4.1 用户数据
- 用户邮箱
- 已选择的频道列表
- 浏览历史记录
- 用户偏好设置

### 4.2 新闻数据
- 新闻ID
- 标题
- 摘要
- 正文内容
- 发布时间
- 来源
- 图片/视频资源

### 4.3 频道数据
- 频道ID
- 频道名称
- 频道描述
- 数据源API信息

